import { AIAnalysisRequest, AIAnalysisResponse, RefactorSuggestion, RefactorType } from './types';

export class AIService {
    private apiKey: string;
    private baseUrl: string = 'https://openrouter.ai/api/v1';
    private model: string = 'qwen/qwen3-coder:free';

    constructor(apiKey: string) {
        this.apiKey = apiKey;
    }

    async analyzeCode(request: AIAnalysisRequest): Promise<AIAnalysisResponse> {
        const startTime = Date.now();
        
        try {
            const prompt = this.buildAnalysisPrompt(request);
            const response = await this.callOpenRouter(prompt);
            const suggestions = this.parseAIResponse(response);
            
            return {
                suggestions,
                analysisTime: Date.now() - startTime,
                tokensUsed: response.usage?.total_tokens || 0
            };
        } catch (error) {
            console.error('AI analysis failed:', error);
            return {
                suggestions: [],
                analysisTime: Date.now() - startTime,
                tokensUsed: 0
            };
        }
    }

    private buildAnalysisPrompt(request: AIAnalysisRequest): string {
        const { context, focusFile, analysisType, selection } = request;
        
        let prompt = `You are an expert code refactoring advisor. Analyze the following codebase and provide intelligent refactoring suggestions.

Project Context:
- Language: ${context.language}
- Project Type: ${context.projectType}
- Dependencies: ${context.dependencies.slice(0, 10).join(', ')}
- Total Files: ${context.files.length}

Analysis Type: ${analysisType}
`;

        if (focusFile) {
            const file = context.files.find(f => f.path === focusFile);
            if (file) {
                prompt += `\nFocus File: ${file.path}\n\`\`\`${file.language}\n${file.content}\n\`\`\`\n`;
            }
        }

        if (selection) {
            prompt += `\nSelected Code Range: Lines ${selection.start.line}-${selection.end.line}\n`;
        }

        if (analysisType === 'full') {
            // Include a summary of all files for full analysis
            prompt += '\nProject Structure:\n';
            context.files.slice(0, 20).forEach(file => {
                prompt += `- ${file.path} (${file.language}, ${file.size} bytes)\n`;
            });
        }

        prompt += `
Please provide refactoring suggestions in the following JSON format:
{
  "suggestions": [
    {
      "id": "unique-id",
      "title": "Brief title",
      "description": "Detailed description",
      "type": "extract-method|extract-class|move-method|rename|inline|design-pattern|architecture|performance|maintainability|security|project-structure",
      "severity": "low|medium|high",
      "confidence": 0.8,
      "file": "path/to/file.ts",
      "range": {
        "start": {"line": 10, "character": 0},
        "end": {"line": 20, "character": 0}
      },
      "changes": [
        {
          "file": "path/to/file.ts",
          "type": "modify|create|delete|move",
          "content": "new code content",
          "diff": "unified diff format"
        }
      ],
      "explanation": "Why this refactoring is beneficial",
      "tradeoffs": ["Potential downside 1", "Potential downside 2"],
      "estimatedEffort": "low|medium|high"
    }
  ]
}

Focus on:
1. Code smells and anti-patterns
2. Performance improvements
3. Maintainability enhancements
4. Security vulnerabilities
5. Architectural improvements
6. Design pattern opportunities
7. Project structure optimizations

Provide 3-8 high-quality suggestions with detailed explanations and trade-offs.`;

        return prompt;
    }

    private async callOpenRouter(prompt: string): Promise<any> {
        const response = await fetch(`${this.baseUrl}/chat/completions`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.apiKey}`,
                'Content-Type': 'application/json',
                'HTTP-Referer': 'https://github.com/refactor-advisor',
                'X-Title': 'Refactor Advisor VS Code Extension'
            },
            body: JSON.stringify({
                model: this.model,
                messages: [
                    {
                        role: 'user',
                        content: prompt
                    }
                ],
                temperature: 0.3,
                max_tokens: 4000,
                top_p: 0.9
            })
        });

        if (!response.ok) {
            throw new Error(`OpenRouter API error: ${response.status} ${response.statusText}`);
        }

        return await response.json();
    }

    private parseAIResponse(response: any): RefactorSuggestion[] {
        try {
            const content = response.choices[0]?.message?.content;
            if (!content) {
                return [];
            }

            // Extract JSON from the response
            const jsonMatch = content.match(/\{[\s\S]*\}/);
            if (!jsonMatch) {
                return [];
            }

            const parsed = JSON.parse(jsonMatch[0]);
            return parsed.suggestions || [];
        } catch (error) {
            console.error('Failed to parse AI response:', error);
            return this.createFallbackSuggestions();
        }
    }

    private createFallbackSuggestions(): RefactorSuggestion[] {
        return [
            {
                id: 'fallback-1',
                title: 'AI Analysis Unavailable',
                description: 'Unable to connect to AI service. Please check your API key and try again.',
                type: RefactorType.MAINTAINABILITY,
                severity: 'low',
                confidence: 0,
                file: '',
                changes: [],
                explanation: 'The AI service is currently unavailable.',
                tradeoffs: [],
                estimatedEffort: 'low'
            }
        ];
    }

    async testConnection(): Promise<boolean> {
        try {
            const response = await fetch(`${this.baseUrl}/models`, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`
                }
            });
            return response.ok;
        } catch (error) {
            return false;
        }
    }
}

